{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port=3000", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "pretty": "prettier --write \"./**/*.{js,jsx,mjs,cjs,ts,tsx,json}\"", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@sereneinserenade/tiptap-comment-extension": "^0.1.2", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-query": "^5.56.2", "@tiptap/extension-color": "^2.23.0", "@tiptap/extension-font-family": "^2.23.0", "@tiptap/extension-highlight": "^2.23.0", "@tiptap/extension-image": "^2.23.0", "@tiptap/extension-link": "^2.23.0", "@tiptap/extension-placeholder": "^2.24.1", "@tiptap/extension-table": "^2.23.0", "@tiptap/extension-table-cell": "^2.23.0", "@tiptap/extension-table-header": "^2.23.0", "@tiptap/extension-table-row": "^2.23.0", "@tiptap/extension-task-item": "^2.23.0", "@tiptap/extension-task-list": "^2.23.0", "@tiptap/extension-text-align": "^2.23.0", "@tiptap/extension-text-style": "^2.23.0", "@tiptap/extension-underline": "^2.23.0", "@tiptap/react": "^2.23.0", "@tiptap/starter-kit": "^2.23.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.5.0", "input-otp": "^1.4.2", "lucide-react": "^0.462.0", "mammoth": "^1.9.1", "next-themes": "^0.3.0", "react": "^18.3.1", "react-color": "^2.19.3", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-pdf": "^9.2.1", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tiptap-extension-pagination": "^2.1.4", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^1.3.1", "@vitest/ui": "^1.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jsdom": "^24.0.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "prettier": "^3.5.3", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vitest": "^1.3.1"}}