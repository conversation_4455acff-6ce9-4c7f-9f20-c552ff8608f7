<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Printable Table Example</title>
    <style>
      body {
        font-family: sans-serif;
        margin: 20mm;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }

      th,
      td {
        border: 1px solid #444;
        padding: 8px;
        text-align: left;
      }

      thead {
        background: #f0f0f0;
      }

      @media print {
        table {
          page-break-inside: auto;
          break-inside: auto;
        }

        tr {
          page-break-inside: avoid;
          break-inside: avoid;
          page-break-after: auto;
          break-after: auto;
        }

        thead {
          display: table-header-group;
        }

        tfoot {
          display: table-footer-group;
        }
      }
    </style>
  </head>
  <body>
    <h1>Very Long Table</h1>

    <table>
      <thead>
        <tr>
          <th>#</th>
          <th>Name</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <!-- Generate many rows -->
        <script>
          for (let i = 1; i <= 100; i++) {
            document.write(`
            <tr>
              <td>${i}</td>
              <td>Item ${i}</td>
              <td>This is a sample description for row ${i}. It has enough content to create multiple pages when printing.</td>
            </tr>
          `);
          }
        </script>
      </tbody>
    </table>
  </body>
</html>
