<!doctype html>
<html>
  <head>
    <style>
      body {
        font-family: sans-serif;
        margin: 0;
        padding: 0;
      }

      .spacer {
        height: 75vh; /* simulate content before table */
      }

      table {
        width: 100%;
        border-collapse: collapse;
      }

      th,
      td {
        border: 1px solid #ccc;
        padding: 8px;
      }

      thead {
        background-color: #f0f0f0;
      }

      @media print {
        table {
          page-break-inside: auto;
          break-inside: auto;
        }

        tr {
          page-break-inside: avoid;
          break-inside: avoid;
        }

        thead {
          display: table-header-group;
        }

        tfoot {
          display: table-footer-group;
        }

        body {
          margin: 10mm;
        }
      }
    </style>
  </head>
  <body>
    <div class="spacer"></div>

    <table>
      <thead>
        <tr>
          <th>#</th>
          <th>Item</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <!-- Add enough rows to break naturally -->
        <script>
          for (let i = 1; i <= 20; i++) {
            document.write(`
            <tr>
              <td>${i}</td>
              <td>Item ${i}</td>
              <td>Description for row ${i} which is long enough to wrap and force pagination when needed.</td>
            </tr>
          `);
          }
        </script>
      </tbody>
    </table>
  </body>
</html>
