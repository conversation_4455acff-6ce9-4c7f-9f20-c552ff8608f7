<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <meta name="robots" content="noindex, nofollow">
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #FAFBFD;
        }

        @page {
            size: A4;
            margin: 0mm;
            padding: 25.4mm;
        }

       @media print {
        .rm-page-break {
            break-after: page;
            page-break-after: always;
        }

        table {
            page-break-inside: auto;
            break-inside: auto;
        }

        tr {
            page-break-inside: avoid;
            break-inside: avoid;
            page-break-after: auto;
            break-after: auto;
        }

        td, th {
            page-break-inside: auto;
            break-inside: auto;
            page-break-after: auto;
            break-after: auto;
        }

        /* Prevent table headers from repeating on new pages */
        thead {
            display: table-row-group;
        }

        tfoot {
            display: table-footer-group;
        }

        .tiptap {
            outline: none;
            cursor: text;
            line-height: 1.5;
            font-size: 11pt;
            margin: 0 auto;
            background-color: white;
            box-sizing: border-box;
        }

        /* Table-specific styling with print optimization */
        .tiptap table {
            border-collapse: collapse;
            margin: 1rem 0;
            overflow: hidden;
            table-layout: fixed;
            width: 100%;
            page-break-inside: auto;
            border-spacing: 0 !important;
        }

        .tiptap table td,
        .tiptap table th {
            border: 1px solid black;
            box-sizing: border-box;
            min-width: 1em;
            padding: 6px 8px;
            position: relative;
            vertical-align: top;
            word-break: break-word;
        }

        .tiptap table th {
            background-color: #c7c7c7;
            font-weight: bold;
            text-align: left;
        }

        /* Enhanced print styles for exact visual fidelity */
        @media print {
            body {
                background-color: white;
                padding: 0;
                margin: 20mm;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                font-family: sans-serif;
                font-size: 11pt;
                line-height: 1.5;
            }

            .tiptap {
                padding: 0;
                margin: 0;
                max-width: none;
                width: 100%;
            }

            /* Critical: Override any grid display that might cause row sticking */
            .tiptap table tr {
                display: table-row !important;
                page-break-inside: avoid !important;
                break-inside: avoid !important;
            }

            .tiptap table td,
            .tiptap table th {
                display: table-cell !important;
                page-break-inside: avoid !important;
                break-inside: avoid !important;
            }

            /* Prevent header repetition - override any table-header-group */
            .tiptap table thead {
                display: table-row-group !important;
            }
        }
    </style>
</head>
<body>
    <div class="tiptap">
        <h1>Test Document with Large Table</h1>
        <p>This document tests the table row breaking functionality. Press <strong>Ctrl+P</strong> to test printing.</p>
        
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Description</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <tr><td>1</td><td>Item 1</td><td>This is a sample description for row 1. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>2</td><td>Item 2</td><td>This is a sample description for row 2. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>3</td><td>Item 3</td><td>This is a sample description for row 3. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>4</td><td>Item 4</td><td>This is a sample description for row 4. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>5</td><td>Item 5</td><td>This is a sample description for row 5. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>6</td><td>Item 6</td><td>This is a sample description for row 6. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>7</td><td>Item 7</td><td>This is a sample description for row 7. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>8</td><td>Item 8</td><td>This is a sample description for row 8. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>9</td><td>Item 9</td><td>This is a sample description for row 9. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>10</td><td>Item 10</td><td>This is a sample description for row 10. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>11</td><td>Item 11</td><td>This is a sample description for row 11. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>12</td><td>Item 12</td><td>This is a sample description for row 12. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>13</td><td>Item 13</td><td>This is a sample description for row 13. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>14</td><td>Item 14</td><td>This is a sample description for row 14. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>15</td><td>Item 15</td><td>This is a sample description for row 15. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>16</td><td>Item 16</td><td>This is a sample description for row 16. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>17</td><td>Item 17</td><td>This is a sample description for row 17. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>18</td><td>Item 18</td><td>This is a sample description for row 18. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>19</td><td>Item 19</td><td>This is a sample description for row 19. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>20</td><td>Item 20</td><td>This is a sample description for row 20. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>21</td><td>Item 21</td><td>This is a sample description for row 21. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>22</td><td>Item 22</td><td>This is a sample description for row 22. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>23</td><td>Item 23</td><td>This is a sample description for row 23. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>24</td><td>Item 24</td><td>This is a sample description for row 24. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>25</td><td>Item 25</td><td>This is a sample description for row 25. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>26</td><td>Item 26</td><td>This is a sample description for row 26. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>27</td><td>Item 27</td><td>This is a sample description for row 27. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>28</td><td>Item 28</td><td>This is a sample description for row 28. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>29</td><td>Item 29</td><td>This is a sample description for row 29. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
                <tr><td>30</td><td>Item 30</td><td>This is a sample description for row 30. It has enough content to test table formatting and page breaking behavior.</td><td>Active</td></tr>
            </tbody>
        </table>
        
        <p>End of test document. The table above should break properly across pages when printed, with rows not sticking together and headers not repeating.</p>
    </div>
</body>
</html>
