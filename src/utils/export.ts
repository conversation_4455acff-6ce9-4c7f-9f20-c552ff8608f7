import { Editor } from "@tiptap/react";

export const exportToHTML = (editor: Editor | null) => {
  if (!editor) return;

  const editorElement = editor.view.dom;

  const clonedContent = editorElement.cloneNode(true) as HTMLElement;

  const uiElements = clonedContent.querySelectorAll(
    ".rm-pagination-gap, .rm-page-header, .rm-page-footer, .comment-highlight"
  );
  uiElements.forEach((el) => el.remove());

  let content = clonedContent.innerHTML;

  const doc = new DOMParser().parseFromString(content, "text/html");
  const tables = doc.querySelectorAll("table");

  tables.forEach((table) => {
    const wrapper = doc.createElement("div");
    wrapper.className = "tableWrapper";
    table.parentNode?.replaceChild(wrapper, table);
    wrapper.appendChild(table);
  });

  content = doc.body.innerHTML;

  // Create the complete HTML document with embedded CSS
  const htmlDocument = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <meta name="robots" content="noindex, nofollow">
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #FAFBFD;
        }

        @page {
            size: A4;
            margin: 25.4mm;
        }
            
        table {
        display: table !important;
        page-break-inside: auto !important;
      }


      tr {
        display: table-row !important;
        page-break-inside: avoid !important;
        break-inside: avoid !important;
      }
        .tiptap {
            outline: none;
            cursor: text;
            line-height: 1.5;
            font-size: 11pt;
            margin: 0 auto;
            background-color: white;
            box-sizing: border-box;
        }
        /* Link styles */
        .tiptap a {
            color: #2563eb;
            cursor: pointer;
        }

        .tiptap a:hover {
            text-decoration: underline;
            text-underline-offset: 4px;
        }

        /* Image specific styling */
        .tiptap img {
            display: block;
            height: auto;
            margin: 1.5rem 0;
            max-width: 100%;
        }

        /* Table-specific styling with print optimization */
        .tiptap table {
            border-collapse: collapse;
            margin: 1rem 0;
            overflow: hidden;
            table-layout: fixed;
            width: 100%;
            page-break-inside: auto;
            border-spacing: 0 !important;
        }

        .tiptap table td,
        .tiptap table th {
            border: 1px solid black;
            box-sizing: border-box;
            min-width: 1em;
            padding: 6px 8px;
            position: relative;
            vertical-align: top;
            word-break: break-word;
            page-break-inside: avoid;
        }

        .tiptap table td > *,
        .tiptap table th > * {
            margin-bottom: 0;
        }

        .tiptap table th {
            background-color: #c7c7c7;
            font-weight: bold;
            text-align: left;
        }

        .tiptap .tableWrapper {
            margin: 1.5rem 0;
            overflow-x: auto;
        }

        .tiptap h1,
        .tiptap h2,
        .tiptap h3,
        .tiptap h4,
        .tiptap h5,
        .tiptap h6 {
            line-height: 1.1;
            text-wrap: pretty;
            font-weight: 300;
        }

        .tiptap h1 {
            font-size: 2rem;
        }
        .tiptap h2 {
            font-size: 1.5rem;
        }
        .tiptap h3 {
            font-size: 1.25rem;
        }
        .tiptap h4 {
            font-size: 1.125rem;
        }
        .tiptap h5{
            font-size: 1rem;
        }
        .tiptap h6 {
            font-size: 0.875rem;
        }

        .tiptap code {
            background-color: #f1f5f9;
            border-radius: 0.375rem;
            color: #1e293b;
            font-size: 0.875rem;
            font-weight: 600;
            padding: 0.125rem 0.25rem;
        }

        .tiptap pre {
            background: #1e293b;
            border-radius: 0.5rem;
            color: #f8fafc;
            font-family: 'JetBrains Mono', 'Menlo', 'Monaco', 'Courier New', monospace;
            margin: 1.5rem 0;
            padding: 0.75rem 1rem;
        }

        .tiptap pre code {
            background: none;
            color: inherit;
            font-size: 0.8rem;
            padding: 0;
        }

        .tiptap blockquote {
            border-left: 3px solid #e2e8f0;
            margin: 1.5rem 0;
            padding-left: 1rem;
        }

        .tiptap hr {
            border: none;
            border-top: 1px solid #e2e8f0;
            margin: 2rem 0;
        }

        .tiptap ul,
        .tiptap ol {
            margin: 0.4rem 1rem 0.4rem 0.4rem;
            padding: 0 1rem;
        }

        .tiptap li {
            margin: 0.25em 0;
        }

        .tiptap ul[data-type="taskList"] {
            list-style: none;
            margin-left: 0;
            padding: 0;
        }

        .tiptap ul[data-type="taskList"] li {
            align-items: flex-start;
            display: flex;
        }

        .tiptap ul[data-type="taskList"] li > label {
            flex: 0 0 auto;
            margin-right: 0.5rem;
            user-select: none;
        }

        .tiptap ul[data-type="taskList"] li > div {
            flex: 1 1 auto;
        }

        .tiptap ul[data-type="taskList"] input[type="checkbox"] {
            cursor: pointer;
        }

    

        .tiptap p:first-child {
            margin-top: 0;
        }

        .tiptap p:last-child {
            margin-bottom: 0;
        }

        .tiptap p.is-editor-empty:first-child::before {
            color: #adb5bd;
            content: attr(data-placeholder);
            float: left;
            height: 0;
            pointer-events: none;
        }

        .tiptap p br.ProseMirror-trailingBreak:only-child {
            display: block;
            height: 1.5em;
        }

        .tiptap p:empty::before {
            content: " ";
            display: inline-block;
            height: inherit;
        }

        /* Enhanced print styles for exact visual fidelity */
        @media print {
            body {
                background-color: white;
                padding: 0;
                margin: 0;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .tiptap {
                padding: 0;
                margin: 0;
                max-width: none;
                width: 100%;
            }

            .document-container {
                border: none;
                margin: 0;
                min-height: auto;
            }

            /* Ensure page breaks are respected */
            .print-page-break {
                page-break-before: always;
                break-before: page;
            }

            .print-page-break.first-page {
                page-break-before: auto;
                break-before: auto;
            }

            /* Hide any remaining UI elements */
            .rm-pagination-gap,
            .rm-page-header,
            .rm-page-footer,
            .comment-highlight {
                display: none !important;
            }
        }
    </style>
</head>
<body>
        <div class="tiptap">
            ${content}
        </div>
</body>
</html>`;

  // Create and download the file
  const blob = new Blob([htmlDocument], { type: "text/html" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = "document.html";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};
